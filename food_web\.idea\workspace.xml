<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="0a1b079a-4f9b-48be-9cc6-50616e9eed76" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="2mEcODbPvaz6cmjUvKXCHOucpHM" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0a1b079a-4f9b-48be-9cc6-50616e9eed76" name="Default Changelist" comment="" />
      <created>1726645141824</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1726645141824</updated>
    </task>
    <servers />
  </component>
  <component name="WindowStateProjectService">
    <state x="272" y="58" key="SettingsEditor" timestamp="1726645154972">
      <screen x="0" y="0" width="1536" height="824" />
    </state>
    <state x="272" y="58" key="SettingsEditor/0.0.1536.824@0.0.1536.824" timestamp="1726645154972" />
  </component>
</project>