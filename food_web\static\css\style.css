* {
	box-sizing: border-box;
	margin:0;
	padding:0;
	}
*::after {
	box-sizing: border-box;
	}
*::before {
	box-sizing: border-box;
	}
body {
	font: 14px/150% microsoft yahei,<PERSON>homa,<PERSON><PERSON>;
	text-align: center;
	text-size-adjust: 100%;
	min-width: 320px;
	color: #2c1810;
	margin: 0;
	padding: 0 0 20px 0;
	background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
	}
a {
	outline: none;
	color: #ff6b35;
	text-decoration: none;
	transition: 0.2s;
	}
a:hover {
	color: #d2691e;
	}
::-webkit-scrollbar {/*Chrome适用*/
	width: 10px;
	height: 1px;
	}
::-webkit-scrollbar-thumb {/*滚动条*/
	border-radius: 10px;
	background: #959da9;
	}
::-webkit-scrollbar-track {/*滚动条里面轨道*/
	border-radius: 10px;
	background: #dce1e6;
	opacity: 0.7;
	}
img {
	border: none
	}
input,select,textarea {
	font-family: microsoft yahei,Helvetica,Tahoma,<PERSON><PERSON>,sans-serif;
	outline: none;
	border: none;
	background: #e9eef2;
	}
.fleft {
	float: left
	}
.fright {
	float: right
	}
.clear {
	clear: both
	}
.Col3 {
	width: 33%;
	}
.Top {
	background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff8c42 100%);
	height: 48px;
	box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
	}
.Line {
	background: rgba(120,130,150,.15);
	height: 1px;
	margin: 5px;
	}
.Logo {
	background: url(../images/logo_ued.svg) no-repeat;
	width: 100px;
	height: 24px;
	margin: 12px;
	float: left
	}
.NavMenu { /*所有产品*/
	background: url(../images/ico_menu.svg) #8b4513 no-repeat center;
	width: 48px;
	height: 48px;
	float: left;
	border-radius: 0 8px 8px 0;
	}
.NavMenu:hover {
	background-color: #d2691e;
	transform: scale(1.05);
	}
.NavMenu .SubMenu { /*子菜单*/
	border-right: 1px solid rgba(255, 107, 53, 0.3);
	width: 210px;
	background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
	position: absolute;
	top: 48px;
	bottom: 0;
	left: 0px;
	animation: fadeIn 0.2s;
	transition: 0.2s;
	box-shadow: 2px 0 10px rgba(255, 107, 53, 0.2);
	}

.NavMenu:hover .SubMenu:before,
.NavMenu:hover .SubMenu ul:before { /*加大甜区*/
	content: "";
	position: absolute;
	width: 100%;
	left: 210px;
	bottom: 0;
	top: 0;
	}
.NavMenu .SubMenu ul {
	padding: 20px 0 0 0;
	}
.NavMenu .SubMenu li {
	border-bottom: 1px solid rgba(120,130,150,.15);
	line-height: 36px;
	text-align: left;
	padding: 0 32px 0 20px;
	list-style: none
	}
.NavMenu .SubMenu li:first-child {
	border-top: 1px solid rgba(120,130,150,.15);
	}
.NavMenu .SubMenu .arrow {
	background: url(../images/ico_arrow.svg) center right 10px no-repeat;
	background-size: 14px;
	transition: 0.2s
	}
.NavMenu .arrow:hover {
	background-position: center right 5px !important;
	}
.NavMenu .SubMenu a {
	color: #8b4513;
	width: 100%;
	height: 6%;
	display: block;
	font-weight: 500;
	}
.NavMenu .SubMenu li:hover {
	background: linear-gradient(90deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.2) 100%);
	border-left: 3px solid #ff6b35;
	}
.NavMenu .SubMenu li a:hover {
	color: #d2691e;
	}
.NavMenu .SubMenu li ul { /*二级菜单*/
	border-right: 1px solid rgba(120,130,150,.2);
	box-shadow: 0 0 10px rgba(120,130,150,.1);
	background: #F6F7F8;
	top: 0;
	bottom: 0;
	left: 210px;
	width: 210px;
	position: absolute;
	display: none;
	transition: 0.2s
	}
.NavMenu .SubMenu li:hover ul {
	animation: fadeIn2 0.2s;
	display: block
	}
.NavMenu li li:hover .PopMenu {
	display: block;
	}
.NavMenu .PopMenu .Search {
	background: url(../images/ico_zoom.svg) no-repeat 5px center #FFF;
	background-size: 22px;
	border-radius: 6px;
	border: 1px solid rgba(120,130,150,.5);
	padding: 0 0 0 32px;
	margin: 5px;
	height: 32px;
	width: 570px;
	}
.NavMenu .QuickMenu {
	width: 210px;
	float: right;
	}
.NavMenu .QuickMenu a:hover {
	border-left: 5px solid #FF7800;
	background: rgba(255,120,0,.1)
	}
.NavMenu .PopMenuList {
	border-right: 1px solid rgba(120,130,150,.1);
	position: absolute;
	left: 10px;
	right: 230px;
	top: 60px;
	bottom: 10px;
	overflow: auto;
	}
.NavMenu .M .PopMenuList {
	border: none;
	right: 10px;
	top: 10px;
	}
.NavMenu .PopMenuList h4 {
	border-bottom: 1px solid rgba(120,130,150,.2);
	margin: 0 10px;
	padding: 0 0 0 16px;
	position: relative
	}
.NavMenu .PopMenuList h4:before {
	content: "";
	width: 8px;
	height: 8px;
	position: absolute;
	left: 0;
	top: 14px;
	border-radius: 50%;
	background: #00a4ff
	}
.UserInfo {
	float: right;
	padding: 4px 30px 0 10px;
	min-width: 140px
	}
.UserInfo:after {
	content: "";
	position: absolute;
	right: 10px;
	top: 20px;
	background: url(../images/ico_arrow.svg) no-repeat;
	transform: rotate(90deg);
	width: 14px;
	height: 14px;
	}
.Face {
	line-height: 40px;
	padding: 4px;
	text-align: left;
	color: #FFF
	}
.Face img {
	width: 40px;
	height: 40px;
	float: left;
	margin: 0 5px 0 0;
	border-radius: 50%;
	border: 1px solid rgba(120,130,150,.1)
	}
.ShowMenu {
	position: relative;
	}
.ShowMenu:hover .MenuSelect {
	animation: fadeInX 0.2s;
	top: 44px;
	display: block
	}
.MenuSelect {
	background: rgba(255,255,255,.8);
	backdrop-filter: saturate(180%) blur(20px);
	-webkit-backdrop-filter: saturate(180%) blur(20px);
	min-width: 130px;
	border-radius: 6px;
	padding: 10px 0;
	box-shadow: 0px 1px 5px rgba(0,0,0,0.3);
	text-align: center;
	float: right;
	position: absolute;
	z-index: 1;
	display: none
	}
.MenuSelect a {
	line-height: 32px;
	font-weight: normal;
	color: rgba(120,130,150,.9);
	display: block
	}
.MenuSelect a:hover {
	background-color: rgba(120,130,150,.25);
	color: #00a4ff;
	}
.TopSearch {
	background: url(../images/ico_search.svg) no-repeat center;
	color: #FFF;
	transition: 0.2s;
	}
.TopSearch:hover {
	background-color: rgba(120,130,150,.3);
	background-position: 12px center;
	background-size: 24px 24px;
	padding: 0 10px 0 44px;
	width: 200px;
	}
.TopSearch::-webkit-input-placeholder {
	color: rgba(255,255,255,0)
	}
.TopSearch:hover::-webkit-input-placeholder {
	color: rgba(255,255,255,.2)
	}
.TopSearch:-ms-input-placeholder {
	color: rgba(255,255,255,0)
	}
.TopSearch:hover::-webkit-input-placeholder {
	color: rgba(255,255,255,.2)
	}
.TopIco {
	width: 48px;
	height: 48px;
	background-repeat: no-repeat;
	background-size: 24px;
	background-position: center;
	vertical-align: middle;
	float: left;
	opacity: 0.7
	}
.TopIco:hover {
	background-color: rgba(120,130,150,.3);
	opacity: 1
	}
.icoRing {
	background-image: url(../images/ico_ring.svg);
	}
.icoFullScreen {
	background-image: url(../images/ico_fullscreen.svg);
	}
.icoSkin {
	background-image: url(../images/ico_magic.svg);
	}
.FadeIn { /*出现*/
	animation: fadeIn 0.2s;
	}
@keyframes fadeIn {
	0% {
		transform: translate3d(-100%, 0, 0)
		}
	to {
		transform: none
		}
	}
@keyframes fadeInX {
	0% {
		transform: translate3d(0, 10%, 0)
		}
	to {
		transform: none
		}
	}
@keyframes fadeIn2 {
	50% {
		transform: translate3d(-10%, 0, 0);
		}
	to {
		transform: none;
		}
	}