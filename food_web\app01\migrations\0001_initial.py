# Generated by Django 2.0 on 2023-04-17 13:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Food',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON><PERSON>(max_length=1024)),
                ('href', models.Char<PERSON>ield(max_length=1024)),
                ('peiliao', models.CharField(max_length=1024)),
                ('rate', models.Char<PERSON>ield(max_length=1024)),
                ('food_id', models.Char<PERSON>ield(max_length=1024)),
                ('img', models.<PERSON><PERSON><PERSON><PERSON>(max_length=1024)),
                ('caipu', models.Char<PERSON>ield(max_length=1024)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=16, unique=True)),
                ('password', models.<PERSON><PERSON><PERSON><PERSON>(max_length=16)),
                ('age', models.Char<PERSON>ield(max_length=16)),
                ('types_interest', models.CharField(max_length=16)),
            ],
        ),
    ]
