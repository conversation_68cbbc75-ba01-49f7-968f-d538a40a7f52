from django.shortcuts import redirect
from django.http import HttpResponseForbidden
from functools import wraps
from app01.models import User


def login_required(view_func):
    """
    检查用户是否已登录的装饰器
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.session.get('islogin', False):
            return redirect('/login/')
        return view_func(request, *args, **kwargs)
    return wrapper


def role_required(allowed_roles):
    """
    检查用户角色权限的装饰器
    allowed_roles: 允许访问的角色列表，如 ['customer'] 或 ['merchant', 'admin']
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查是否登录
            if not request.session.get('islogin', False):
                return redirect('/login/')
            
            # 获取用户角色
            user_role = request.session.get('user_role')
            if not user_role:
                # 如果session中没有角色信息，从cookie中获取用户名并查询数据库
                user_name = request.get_signed_cookie("name", None)
                if user_name:
                    try:
                        user = User.objects.get(name=user_name)
                        user_role = user.role
                        request.session['user_role'] = user_role
                    except User.DoesNotExist:
                        return redirect('/login/')
                else:
                    return redirect('/login/')
            
            # 检查角色权限
            if user_role not in allowed_roles:
                return HttpResponseForbidden("您没有权限访问此页面")
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def customer_required(view_func):
    """订单人权限装饰器"""
    return role_required(['customer'])(view_func)


def merchant_required(view_func):
    """商家权限装饰器"""
    return role_required(['merchant'])(view_func)


def admin_required(view_func):
    """管理员权限装饰器"""
    return role_required(['admin'])(view_func)


def merchant_or_admin_required(view_func):
    """商家或管理员权限装饰器"""
    return role_required(['merchant', 'admin'])(view_func)
