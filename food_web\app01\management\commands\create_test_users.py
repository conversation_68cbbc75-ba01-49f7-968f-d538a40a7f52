from django.core.management.base import BaseCommand
from app01.models import User


class Command(BaseCommand):
    help = '创建测试用户'

    def handle(self, *args, **options):
        # 创建管理员用户
        admin_user, created = User.objects.get_or_create(
            name='admin',
            defaults={
                'password': '123456',
                'age': '30',
                'types_interest': '川菜',
                'role': 'admin'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('创建管理员用户: admin / 123456'))
        else:
            self.stdout.write(self.style.WARNING('管理员用户已存在: admin'))

        # 创建商家用户
        merchant_user, created = User.objects.get_or_create(
            name='merchant',
            defaults={
                'password': '123456',
                'age': '35',
                'types_interest': '粤菜',
                'role': 'merchant'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('创建商家用户: merchant / 123456'))
        else:
            self.stdout.write(self.style.WARNING('商家用户已存在: merchant'))

        # 创建订单人用户
        customer_user, created = User.objects.get_or_create(
            name='customer',
            defaults={
                'password': '123456',
                'age': '25',
                'types_interest': '湘菜',
                'role': 'customer'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('创建订单人用户: customer / 123456'))
        else:
            self.stdout.write(self.style.WARNING('订单人用户已存在: customer'))

        self.stdout.write(self.style.SUCCESS('\n=== 测试账号信息 ==='))
        self.stdout.write(self.style.SUCCESS('管理员: admin / 123456'))
        self.stdout.write(self.style.SUCCESS('商家: merchant / 123456'))
        self.stdout.write(self.style.SUCCESS('订单人: customer / 123456'))
        self.stdout.write(self.style.SUCCESS('=================='))
