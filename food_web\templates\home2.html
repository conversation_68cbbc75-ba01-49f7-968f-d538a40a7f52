{% load static %}

<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="no-referrer"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery-3.4.0.min.js' %}"></script>
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.min.css' %}">
<!--    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">-->
    <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
<!--    <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>-->
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>美食推荐系统</title>
</head>
<body>
<div class="Top">
    <div class="NavMenu">
        <div class="SubMenu">
            <ul>
                <li>
                    <a href="/home/<USER>/">美食展示</a>
                    <a href="/home_reco/">系统推荐</a>
                    <a href="/home_reco/">算法推荐</a>
                    <a href="/home2/">配料矩形图</a>
                    <a href="/home3/">评分占比图</a>
                    <a href="/home4/">类别柱状图</a>
                    <a href="/home5/">配料柱状图</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="Logo"></div>

    <!--    个人工作台-->
    <div class="UserInfo ShowMenu">

        <a class="Face" href="#"><img src="{% static 'images/zqz.png' %}" alt="zhangqzh"/>{{user_name}}</a>
        <div class="MenuSelect">
            <a href="#">我的班务</a>
            <a href="#">工作数据</a>
            <a href="#">系统设置</a>
            <div class="Line"></div>
            <a href="/logout/">退出系统</a>
        </div>
    </div>


</div>
<div style="margin-left:10%;">
    <h1>配料矩形图</h1>
</div>
<div style="width:1000px;margin-top:10px;margin-left:22%;text-align:center">
    <div id="main" style="width: 1000px;height:400px;;margin:0 auto;margin-top:100px;display: inline-block;"></div>
</div>
</body>
</html>


<script>
    var myChart = echarts.init(document.getElementById('main'));
        // 显示标题，图例和空的坐标轴
        myChart.setOption({
            series: [
                    {
                      type: 'treemap',
                      data: []
                    }
                  ]
        });


        // 异步加载数据
        $.get('http://127.0.0.1:8000/home2_data/').done(function (data2) {
            // 填入数据
            myChart.setOption({

                series: [{
                    // 根据名字对应到相应的系列
                    name: '数量',
                    data: $.parseJSON(data2)
                }]
            });
        });


</script>