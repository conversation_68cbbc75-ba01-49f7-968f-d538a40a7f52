{% load static %}

<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="no-referrer"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery-3.4.0.min.js' %}"></script>
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
<!--    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css"-->
<!--          integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">-->
    <link rel="stylesheet" type="text/css" href="{% static 'css/bootstrap.min.css' %}">
    <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
    <!--    <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>-->

    <title>美食推荐系统</title>
</head>
<body>
<div class="Top">
    <div class="NavMenu">
        <div class="SubMenu">
            <ul>
                <li>
                    <a href="/home/<USER>/">美食展示</a>
                    <a href="/home_reco/">系统推荐</a>
                    <a href="/home_reco/">算法推荐</a>
                    <a href="/home2/">配料矩形图</a>
                    <a href="/home3/">评分占比图</a>
                    <a href="/home4/">类别柱状图</a>
                    <a href="/home5/">配料柱状图</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="Logo"></div>

    <!--    个人工作台-->
    <div class="UserInfo ShowMenu">

        <a class="Face" href="#"><img src="{% static 'images/zqz.png' %}" alt="zhangqzh"/>{{user_name}}</a>
        <div class="MenuSelect">
            <a href="#">我的班务</a>
            <a href="#">工作数据</a>
            <a href="#">系统设置</a>
            <div class="Line"></div>
            <a href="/logout/">退出系统</a>
        </div>
    </div>


</div>
<div>
    <form class="form-inline" action="/home/<USER>/" method="get">
        <div class="form-group" style="margin-left:60%;">
            <input type="text" class="form-control" name="word" placeholder="请输入名称"
                   style="width:200px">
        </div>
        <button type="submit" class="btn btn-default">搜索</button>
    </form>
</div>
<input type="text" id="food_type" value="{{ food_type }}" style="display:none;">
<div style="width:1000px;margin-top:10px;margin-left:22%;text-align:center">
    <!--    '北京小吃','天津小吃','河北小吃','山西小吃','蒙古小吃','上海小吃','山东小吃','江苏小吃','浙江小吃','安徽小吃''吉林小吃','辽宁小吃'-->
    <!--    ,'陕西小吃','新疆小吃','宁夏小吃','甘肃小吃','青海小吃','湖北小吃','湖南小吃','河南小吃','江西小吃','重庆小吃','四川小吃','云南小吃','贵州小吃','西藏小吃','广东小吃','福建小吃'-->
    <!--    ,'广西小吃','海南小吃','香港小吃','台湾小吃','成都小吃','黑龙江小吃'-->
    <div>
        <ul class="nav nav-pills">
            <li role="presentation" data-value="全部"><a href="/home/<USER>/">全部</a></li>
            <li role="presentation" data-value="北京小吃"><a href="/home/<USER>/">北京小吃</a></li>
            <li role="presentation" data-value="天津小吃"><a href="/home/<USER>/">天津小吃</a></li>
            <li role="presentation" data-value="河北小吃"><a href="/home/<USER>/">河北小吃</a></li>
            <li role="presentation" data-value="山西小吃"><a href="/home/<USER>/">山西小吃</a></li>
            <li role="presentation" data-value="蒙古小吃"><a href="/home/<USER>/">蒙古小吃</a></li>
            <li role="presentation" data-value="上海小吃"><a href="/home/<USER>/">上海小吃</a></li>
        </ul>
    </div>
    <div class="row" style="margin-top:10px;">
        {% for food in food_li %}
        <div class="col-sm-6 col-md-3" style="table-layout:fixed; width: 250px;">
            <div class="thumbnail" style="table-layout:fixed; width: 240px;">
                <div>
                    <a href="/home_food_detail/{{ food.id }}/">
                        <img src="{{ food.img }}" alt="{{ food.title }}">
                    </a>

                </div>
                <div class="caption">
                    <p class="lineOverflow" style="font-weight: bold;">{{ food.title }}</p>
                    <p style="">{{food.rate}} {{food.caipu}}</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    <div id="pages" class="text-center">
        {% if word %}
        {# 实现分页标签的代码 #}
        {# 这里使用 bootstrap 渲染页面 #}
        <nav>
            <ul class="pagination">
                <li class="step-links">
                    {% if food_li.has_previous %}
                    <a class='active' href="?page={{ food_li.previous_page_number }}&word={{ word }}">上一页</a>
                    {% endif %}

                    <span class="current">
                    Page {{ food_li.number }} of {{ food_li.paginator.num_pages }}</span>

                    {% if food_li.has_next %}
                    <a class='active' href="?page={{ food_li.next_page_number }}&word={{ word }}">下一页</a>
                    {% endif %}
                </li>
            </ul>
        </nav>
        {% else %}
        {# 实现分页标签的代码 #}
        {# 这里使用 bootstrap 渲染页面 #}
        <nav>
            <ul class="pagination">
                <li class="step-links">
                    {% if food_li.has_previous %}
                    <a class='active' href="?page={{ food_li.previous_page_number }}">上一页</a>
                    {% endif %}

                    <span class="current">
                    Page {{ food_li.number }} of {{ food_li.paginator.num_pages }}</span>

                    {% if food_li.has_next %}
                    <a class='active' href="?page={{ food_li.next_page_number }}">下一页</a>
                    {% endif %}
                </li>
            </ul>
        </nav>

        {% endif %}
    </div>

</div>
</body>
</html>

<script>
    var food_type = document.getElementById('food_type').value;
    if(food_type=="全部"){
        ex = $("li[data-value='全部']");
    }else if(food_type=="北京小吃"){
        ex = $("li[data-value='北京小吃']");
    }else if(food_type=="天津小吃"){
        ex = $("li[data-value='天津小吃']");
    }else if(food_type=="河北小吃"){
        ex = $("li[data-value='河北小吃']");
    }else if(food_type=="山西小吃"){
        ex = $("li[data-value='山西小吃']");
    }else if(food_type=="蒙古小吃"){
        ex = $("li[data-value='蒙古小吃']");
    }else if(food_type=="上海小吃"){
        ex = $("li[data-value='上海小吃']");
    }

    ex[0].className = "active";



</script>
<style>
    .lineOverflow {
        width: 200px;
        text-overflow: ellipsis;
        white-space: nowrap;/*禁止自动换行*/
        overflow: hidden;
    }

</style>