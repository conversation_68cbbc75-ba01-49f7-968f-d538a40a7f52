{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>菜品管理 - 商家管理中心</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .food-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
        }
        .table th {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 15px;
        }
        .table td {
            padding: 15px;
            vertical-align: middle;
            border-color: #ffeee0;
        }
        .food-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .search-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(255, 107, 53, 0.1);
        }
        .btn-action {
            margin-right: 5px;
            border-radius: 20px;
            padding: 5px 15px;
        }
        .sidebar {
            background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #8b4513;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🍽️ 菜品管理</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #8b4513; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link active" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #ff6b35;">
                        <h6 style="color: #8b4513; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <form method="get" class="d-flex">
                                <input type="text" name="word" class="form-control" 
                                       placeholder="搜索菜品名称..." value="{{ word }}">
                                <button type="submit" class="btn btn-primary ml-2" 
                                        style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                                    🔍 搜索
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4 text-right">
                            <a href="/merchant/foods/add/" class="btn btn-success" 
                               style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none;">
                                ➕ 添加新菜品
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 菜品表格 -->
                <div class="food-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>图片</th>
                                <th>菜品名称</th>
                                <th>类别</th>
                                <th>评分</th>
                                <th>配料</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for food in food_li %}
                            <tr>
                                <td>
                                    <img src="{{ food.img }}" alt="{{ food.title }}" class="food-image" 
                                         onerror="this.src='{% static 'images/default-food.jpg' %}'">
                                </td>
                                <td>
                                    <strong style="color: #8b4513;">{{ food.title }}</strong>
                                    <br>
                                    <small class="text-muted">ID: {{ food.food_id }}</small>
                                </td>
                                <td>
                                    <span class="badge badge-primary" 
                                          style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);">
                                        {{ food.caipu }}
                                    </span>
                                </td>
                                <td>
                                    <span style="color: #ff6b35;">⭐ {{ food.rate }}</span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% for ingredient in food.peiliao|split:","|slice:":3" %}
                                            {{ ingredient.strip }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                        {% if food.peiliao|split:","|length > 3 %}...{% endif %}
                                    </small>
                                </td>
                                <td>
                                    <a href="/merchant/foods/edit/{{ food.id }}/" class="btn btn-sm btn-primary btn-action">
                                        ✏️ 编辑
                                    </a>
                                    <button class="btn btn-sm btn-danger btn-action" 
                                            onclick="confirmDelete({{ food.id }}, '{{ food.title }}')">
                                        🗑️ 删除
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-5">
                                    <h5>暂无菜品数据</h5>
                                    <p>点击上方"添加新菜品"按钮开始添加菜品</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if food_li.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if food_li.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ food_li.previous_page_number }}{% if word %}&word={{ word }}{% endif %}">上一页</a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ food_li.number }} / {{ food_li.paginator.num_pages }}</span>
                        </li>
                        
                        {% if food_li.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ food_li.next_page_number }}{% if word %}&word={{ word }}{% endif %}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        function confirmDelete(foodId, foodTitle) {
            if (confirm('确定要删除菜品 "' + foodTitle + '" 吗？此操作不可恢复。')) {
                window.location.href = '/merchant/foods/delete/' + foodId + '/';
            }
        }
    </script>
</body>
</html>
