from django.db import models

# Create your models here.

# Create your models here.
class User(models.Model):
    ROLE_CHOICES = [
        ('customer', '订单人'),
        ('merchant', '商家'),
        ('admin', '管理员'),
    ]

    name = models.CharField(max_length=16,unique=True)
    password = models.CharField(max_length=16)
    age = models.CharField(max_length=16)
    types_interest = models.CharField(max_length=16)
    role = models.Char<PERSON>ield(max_length=10, choices=ROLE_CHOICES, default='customer')

    @classmethod
    def create(cls,name,password,age,types_interest,role='customer'):
        return cls(name=name,password=password,age=age,types_interest=types_interest,role=role)

    def __str__(self):
        return f"{self.name} ({self.get_role_display()})"


class Food(models.Model):
    title = models.Char<PERSON>ield(max_length=1024)
    href = models.Cha<PERSON><PERSON><PERSON>(max_length=1024)
    peiliao = models.Char<PERSON><PERSON>(max_length=1024)
    rate = models.Char<PERSON>ield(max_length=1024)
    food_id = models.CharField(max_length=1024)
    img = models.Char<PERSON>ield(max_length=1024)
    caipu = models.CharField(max_length=1024)


    @classmethod
    def create(cls,title,href,peiliao,rate,food_id,img,caipu):
        return cls(title=title,href=href,peiliao=peiliao,rate=rate,food_id=food_id,
                   img=img,caipu=caipu)

    def __str__(self):
        return self.title


#美食评分
class FoodRating(models.Model):
    food_id = models.CharField(max_length=1024)  #美食id
    name = models.CharField(max_length=1024)  #
    user_id = models.CharField(max_length=16)  # 用户id
    rating = models.CharField(max_length=16)  # 评分

    @classmethod
    def create(cls,food_id,name,user_id,rating):
        return cls(food_id=food_id,name=name,user_id=user_id,rating=rating)

    def __str__(self):
        return self.food_id