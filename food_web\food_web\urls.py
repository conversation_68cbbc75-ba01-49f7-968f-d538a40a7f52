"""food_web URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, re_path
from app01 import views
from django.views.generic import RedirectView  # 添加这行导入

urlpatterns = [
    # 添加根路径重定向 - 选择以下一种方式
    path('', RedirectView.as_view(url='/login/')),  # 方式1：重定向到登录页
    # 或者
    # re_path(r'^$', views.login, name='home'),  # 方式2：直接指向登录视图

    path('admin/', admin.site.urls),
    # 用户
    re_path(r'^login/', views.login),
    re_path(r'^reg/', views.reg),
    re_path(r'^home/(\w+)', views.home),
    re_path(r'^home_food_detail/(\d+)/', views.home_food_detail),
    re_path(r'^home_reco/', views.home_reco),
    re_path(r'^home_algorithm_reco/', views.home_algorithm_reco),
    re_path(r'^home2/', views.home2),
    re_path(r'^home2_data/', views.home2_data),
    re_path(r'^home3/', views.home3),
    re_path(r'^home3_data/', views.home3_data),
    re_path(r'^home4/', views.home4),
    re_path(r'^home4_data/', views.home4_data),
    re_path(r'^home5/', views.home5),
    re_path(r'^home5_data/', views.home5_data),
    re_path(r'^logout/', views.logout),
]
