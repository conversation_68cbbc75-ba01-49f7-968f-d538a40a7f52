from django.shortcuts import render

# Create your views here.
from django.shortcuts import render, HttpResponse, redirect
from app01.models import User, Food,FoodRating
import time,json
from django.core.paginator import <PERSON><PERSON>ator, PageNotAnInteger, EmptyPage, InvalidPage


# 用户登录
def login(request):
    if request.method == "POST":
        name = request.POST.get("name")
        password = request.POST.get("password")
        user_objs = User.objects.filter(name=name).filter(password=password)
        if user_objs.exists():
            user_obj = user_objs.first()
            response = redirect("/home/<USER>/")
            response.set_signed_cookie("name", name)
            request.session['islogin'] = True
            return response
    return render(request, "login.html")


# 用户注册
def reg(request):
    if request.method == "POST":
        name = request.POST.get("name")
        password = request.POST.get("password")
        age = request.POST.get("age")
        types_interest = request.POST.get("types_interest")

        obj = User.create(name, password, age, types_interest)
        obj.save()
        time.sleep(0.5)
        return redirect("/login/")
    return render(request, "reg.html")


# 大厅
def home(request,food_type):
    user_name = request.get_signed_cookie("name")

    if food_type == "全部":  # 全部
        result = Food.objects.all()
        print(len(result))
    else:
        result = Food.objects.filter(caipu__icontains=food_type)

    word = request.GET.get("word")
    if word:
        if food_type == "全部":  #全部
            result = Food.objects.filter(title__icontains=word)
            print(len(result))
        else:
            result = Food.objects.filter(caipu__icontains=food_type).filter(title__icontains=word)

    paginator = Paginator(result, 12)

    if request.method == "GET":
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        except PageNotAnInteger:
            food_li = paginator.page(1)
        except InvalidPage:
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home.html", {"food_li": food_li, "user_name": user_name,"food_type":food_type,"word":word})




#详情页
def home_food_detail(request,id):
    obj = Food.objects.filter(id=id).first()
    user_name = request.get_signed_cookie("name")

    user_id = User.objects.filter(name=user_name).first().id  # 用户id
    rating_obj = FoodRating.objects.filter(food_id=id).filter(user_id=user_id).first()
    if rating_obj == None:
        rating_obj = FoodRating.create(id, user_name, user_id, "0")
        rating_obj.save()
    print(rating_obj)
    if request.method == "POST":
        print(1)
        rating = request.POST.get("rating")
        print("rating",rating)
        rating_obj.rating = rating
        rating_obj.save()
        print(111)
        return redirect(f"/home_food_detail/{id}/")

    return render(request, "home_food_detail.html", {"user_name": user_name, "obj": obj, "rating_obj":rating_obj})



# 大厅
def home_reco(request):
    user_name = request.get_signed_cookie("name")
    types_interest = User.objects.filter(name=user_name).first().types_interest
    result = Food.objects.filter(caipu__icontains=types_interest)

    paginator = Paginator(result, 12)

    if request.method == "GET":
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        except PageNotAnInteger:
            food_li = paginator.page(1)
        except InvalidPage:
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home_reco.html", {"food_li": food_li, "user_name": user_name})




from numpy import *
import time
from texttable import Texttable


class CF:

    def __init__(self, movies, ratings, k=5, n=10):
        self.movies = movies
        self.ratings = ratings
        self.k = k
        self.n = n
        self.userDict = {}

        self.ItemUser = {}
        self.neighbors = []
        self.recommandList = []
        self.cost = 0.0

    def recommendByUser(self, userId):
        self.formatRate()
        self.n = len(self.userDict[userId])
        self.getNearestNeighbor(userId)
        self.getrecommandList(userId)
        self.getPrecision(userId)

    def getrecommandList(self, userId):
        self.recommandList = []
        recommandDict = {}
        for neighbor in self.neighbors:
            movies = self.userDict[neighbor[1]]
            for movie in movies:
                if (movie[0] in recommandDict):
                    recommandDict[movie[0]] += neighbor[0]
                else:
                    recommandDict[movie[0]] = neighbor[0]

        # 建立推荐列表
        for key in recommandDict:
            self.recommandList.append([recommandDict[key], key])
        self.recommandList.sort(reverse=True)
        self.recommandList = self.recommandList[:self.n]

    # 将ratings转换为userDict和ItemUser
    def formatRate(self):
        self.userDict = {}
        self.ItemUser = {}
        for i in self.ratings:
            # 评分最高为5 除以5 进行数据归一化
            temp = (i[1], float(i[2]) / 5)
            # 计算userDict {'1':[(1,5),(2,5)...],'2':[...]...}
            if (i[0] in self.userDict):
                self.userDict[i[0]].append(temp)
            else:
                self.userDict[i[0]] = [temp]
            # 计算ItemUser {'1',[1,2,3..],...}
            if (i[1] in self.ItemUser):
                self.ItemUser[i[1]].append(i[0])
            else:
                self.ItemUser[i[1]] = [i[0]]

    # 找到某用户的相邻用户
    def getNearestNeighbor(self, userId):
        neighbors = []
        self.neighbors = []
        # 获取userId评分的图书都有那些用户也评过分
        for i in self.userDict[userId]:
            for j in self.ItemUser[i[0]]:
                if (j != userId and j not in neighbors):
                    neighbors.append(j)
        # 计算这些用户与userId的相似度并排序
        for i in neighbors:
            dist = self.getCost(userId, i)
            self.neighbors.append([dist, i])
        # 排序默认是升序，reverse=True表示降序
        self.neighbors.sort(reverse=True)
        self.neighbors = self.neighbors[:self.k]

    # 格式化userDict数据
    def formatuserDict(self, userId, l):
        user = {}
        for i in self.userDict[userId]:
            user[i[0]] = [i[1], 0]
        for j in self.userDict[l]:
            if (j[0] not in user):
                user[j[0]] = [0, j[1]]
            else:
                user[j[0]][1] = j[1]
        return user

    # 计算余弦距离
    def getCost(self, userId, l):
        user = self.formatuserDict(userId, l)
        x = 0.0
        y = 0.0
        z = 0.0
        for k, v in user.items():
            x += float(v[0]) * float(v[0])
            y += float(v[1]) * float(v[1])
            z += float(v[0]) * float(v[1])
        if (z == 0.0):
            return 0
        return z / sqrt(x * y)

    # 推荐的准确率
    def getPrecision(self, userId):
        user = [i[0] for i in self.userDict[userId]]
        print("self.recommandList",self.recommandList)
        recommand = [i[1] for i in self.recommandList]
        print(recommand)
        count = 0.0
        if (len(user) >= len(recommand)):
            for i in recommand:
                if (i in user):
                    count += 1.0
            self.cost = count / len(recommand)
        else:
            for i in user:
                if (i in recommand):
                    count += 1.0
            self.cost = count / len(user)

    # 显示推荐列表
    def showTable(self):
        neighbors_id = [i[1] for i in self.neighbors]
        table = Texttable()
        table.set_deco(Texttable.HEADER)
        table.set_cols_dtype(["t", "t", "t", "t"])
        table.set_cols_align(["l", "l", "l", "l"])
        rows = []
        # rows.append([u"movie ID", u"Name", u"release", u"from userID"])
        for item in self.recommandList:
            fromID = []
            for i in self.movies:
                if i[0] == item[1]:
                    movie = i
                    break
            for i in self.ItemUser[item[1]]:
                if i in neighbors_id:
                    fromID.append(i)
            movie.append(fromID)
            rows.append(movie)
        print(rows)
        return rows


# 算法推荐
def home_algorithm_reco(request):
    user_name = request.get_signed_cookie("name")
    print("user_name",user_name)
    user_id = User.objects.filter(name=user_name).first().id
    print("user_id", user_id)
    foods = []
    food_li = Food.objects.all()
    for food in food_li:
        row = []
        row.append(str(food.id))
        row.append(food.title)
        row.append(food.caipu)
        foods.append(row)
    print("foods", foods)
    ratings = []  # 用户id   游戏id  评分
    rating_li = FoodRating.objects.all()
    for rating in rating_li:
        row2 = []
        row2.append(rating.user_id)
        row2.append(rating.food_id)
        row2.append(rating.rating)
        ratings.append(row2)
    print("ratings",ratings)

    demo = CF(foods, ratings, k=20)
    print("demo----",demo)
    demo.recommendByUser(str(user_id))  # 获取当前用户推荐的
    li = demo.showTable()
    print(li)
    result = []
    for i in li:
        obj = Food.objects.filter(id=i[0]).first()
        result.append(obj)
    print(result)
    paginator = Paginator(result[:20], 12)

    if request.method == "GET":
        # 获取 url 后面的 page 参数的值, 首页不显示 page 参数, 默认值是 1
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        # todo: 注意捕获异常
        except PageNotAnInteger:
            # 如果请求的页数不是整数, 返回第一页。
            food_li = paginator.page(1)
        except InvalidPage:
            # 如果请求的页数不存在, 重定向页面
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            # 如果请求的页数不在合法的页数范围内，返回结果的最后一页。
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home_algorithm_reco.html", {"food_li": food_li, "user_name": user_name})






def home2(request):
    user_name = request.get_signed_cookie("name")
    return render(request,"home2.html",{"user_name": user_name})

import copy
def home2_data(request):
    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(","))
    rows2 = copy.deepcopy(rows)
    rows2 = list(set(rows2))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:

        try:
            dic[row] += 1
        except:
            pass
    result = []
    for k, v in dic.items():
        dic3 = {}
        dic3["name"] = f"{k}:{v}"
        dic3["value"] = v
        result.append(dic3)
    print(result)
    return HttpResponse(json.dumps(result))




# 评分占比
def home3(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home3.html",{"user_name":user_name})


def home3_data(request):
    li = Food.objects.all()
    dic = {}
    li2 = []
    for i in li:
        li2.append(i.caipu.strip("小吃"))
    li3 = list(set(li2))
    result = []
    for i3 in li3:
        dic3 = {}
        dic3["name"] = i3
        dic3["value"] = li2.count(i3)
        result.append(dic3)
    print(result)
    return HttpResponse(json.dumps(result))




# 柱状图
def home4(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home4.html",{"user_name":user_name})


def home4_data(request):
    dic4 = {
        "categories": [],
        "data": [],
    }
    li = Food.objects.all()
    rows = list(set([i.caipu for i in li]))
    dic = {}
    for row in rows:
        dic[row] = 0
    for i in li:
        dic[i.caipu]+=1
    print(dic)
    for k, v in dic.items():
        dic4['categories'].append(k)
        dic4['data'].append(v)
    return HttpResponse(json.dumps(dic4))


# 配料柱状图
def home5(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home5.html",{"user_name":user_name})


def home5_data(request):
    dic4 = {
        "categories": [],
        "data": [],
    }
    # li = Food.objects.all()
    #
    # rows = list(set([i.peiliao.split(',') for i in li]))
    # dic = {}
    # for row in rows:
    #     dic[row] = 0
    # for i in li:
    #     dic[i.peiliao.split(',')]+=1
    # print(dic)

    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(','))
    rows2 = copy.deepcopy(rows)
    rows2 = list(set(rows2))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:

        try:
            dic[row] += 1
        except:
            pass


    for k, v in dic.items():
        dic4['categories'].append(k)
        dic4['data'].append(v)
    return HttpResponse(json.dumps(dic4))



#退出功能
def logout(request):
    response = redirect("/login/")
    response.set_signed_cookie("content", "123")
    return response