{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>商家管理中心 - 美食推荐系统</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
            border-left: 5px solid #ff6b35;
            transition: transform 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #8b4513;
        }
        .stats-label {
            color: #666;
            font-size: 1.1rem;
        }
        .quick-action-card {
            background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ff6b35;
            text-align: center;
            transition: all 0.3s;
        }
        .quick-action-card:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
        }
        .quick-action-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .recent-foods-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
        }
        .food-item {
            border-bottom: 1px solid #ffeee0;
            padding: 15px 0;
        }
        .food-item:last-child {
            border-bottom: none;
        }
        .sidebar {
            background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #8b4513;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🏪 商家管理中心</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #8b4513; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #ff6b35;">
                        <h6 style="color: #8b4513; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 统计卡片 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_foods }}</div>
                            <div class="stats-label">📋 总菜品数量</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_ratings }}</div>
                            <div class="stats-label">⭐ 总评价数量</div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="row">
                    <div class="col-md-4">
                        <a href="/merchant/foods/add/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">➕</div>
                                <h5 style="color: #8b4513;">添加新菜品</h5>
                                <p class="text-muted">快速添加新的美食菜品</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/merchant/foods/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">🍽️</div>
                                <h5 style="color: #8b4513;">管理菜品</h5>
                                <p class="text-muted">编辑和管理现有菜品</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/merchant/charts/ingredients/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">📊</div>
                                <h5 style="color: #8b4513;">数据分析</h5>
                                <p class="text-muted">查看销售和评价数据</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 最近菜品 -->
                <div class="recent-foods-card">
                    <h4 style="color: #8b4513; margin-bottom: 20px;">🆕 最近添加的菜品</h4>
                    {% for food in recent_foods %}
                    <div class="food-item">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 style="color: #8b4513;">{{ food.title }}</h6>
                                <p class="text-muted mb-0">{{ food.caipu }} | 评分: {{ food.rate }}</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="/merchant/foods/edit/{{ food.id }}/" class="btn btn-sm btn-outline-primary">编辑</a>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">暂无菜品数据</p>
                    {% endfor %}
                    
                    {% if recent_foods %}
                    <div class="text-center mt-3">
                        <a href="/merchant/foods/" class="btn btn-primary" 
                           style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                            查看所有菜品
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
