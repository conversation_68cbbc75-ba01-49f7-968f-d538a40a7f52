{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美食智能推荐系统 - 登录</title>
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 25%, #ff8c42 50%, #d2691e 75%, #8b4513 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        .login-left {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
        }
        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .login-right {
            padding: 60px 40px;
        }
        .system-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .system-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        .feature-list {
            text-align: left;
            margin-top: 30px;
        }
        .feature-item {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .form-title {
            color: #8b4513;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-control {
            border: 2px solid #ffeee0;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        .role-selection {
            background: #fff8f0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .role-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        .role-option:hover {
            background: #ffeee0;
        }
        .role-option.selected {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border-color: #d2691e;
        }
        .role-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .btn-login {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
            color: white;
        }
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
        .register-link a {
            color: #ff6b35;
            text-decoration: none;
            font-weight: bold;
        }
        .register-link a:hover {
            color: #d2691e;
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .login-left {
                display: none;
            }
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row no-gutters h-100">
            <!-- 左侧介绍区域 -->
            <div class="col-md-6 login-left">
                <div style="position: relative; z-index: 1;">
                    <div class="system-title">🍽️ 美食智能推荐系统</div>
                    <div class="system-subtitle">发现美味，享受生活</div>

                    <div class="feature-list">
                        <div class="feature-item">
                            <span class="feature-icon">🤖</span>
                            <span>智能推荐算法</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🍜</span>
                            <span>丰富美食数据库</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⭐</span>
                            <span>用户评分系统</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📊</span>
                            <span>数据分析报表</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🏪</span>
                            <span>商家管理平台</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="col-md-6 login-right">
                <h2 class="form-title">欢迎登录</h2>

                <form action="/login/" method="post" id="loginForm">
                    <div class="form-group">
                        <input type="text" class="form-control" name="name" placeholder="请输入用户名" required>
                    </div>

                    <div class="form-group">
                        <input type="password" class="form-control" name="password" placeholder="请输入密码" required>
                    </div>

                    <div class="role-selection">
                        <h6 style="color: #8b4513; margin-bottom: 15px;">选择登录角色：</h6>

                        <div class="role-option selected" onclick="selectRole('customer')">
                            <span class="role-icon">👤</span>
                            <div>
                                <strong>订单人</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">浏览美食，查看推荐，评分互动</div>
                            </div>
                            <input type="radio" name="role_hint" value="customer" checked style="display: none;">
                        </div>

                        <div class="role-option" onclick="selectRole('merchant')">
                            <span class="role-icon">🏪</span>
                            <div>
                                <strong>商家</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">管理菜品，查看数据分析</div>
                            </div>
                            <input type="radio" name="role_hint" value="merchant" style="display: none;">
                        </div>

                        <div class="role-option" onclick="selectRole('admin')">
                            <span class="role-icon">👑</span>
                            <div>
                                <strong>管理员</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">系统管理，权重调整，用户管理</div>
                            </div>
                            <input type="radio" name="role_hint" value="admin" style="display: none;">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-login">🚀 立即登录</button>
                </form>

                <div class="register-link">
                    <p>还没有账号？<a href="/reg/">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectRole(role) {
            // 移除所有选中状态
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 添加选中状态
            event.currentTarget.classList.add('selected');

            // 设置对应的radio按钮
            document.querySelector(`input[value="${role}"]`).checked = true;
        }
    </script>
</body>
</html>